import { cookies } from "next/headers";

export class BaseAPI {
  constructor(protected baseURL: string) {}

  /**
   * Makes a request to the API with JSON data
   */
  protected async request<T>(path: string, options: RequestInit): Promise<T> {
    const cookieStore = await cookies();
    const lang = cookieStore.get("NEXT_LOCALE")?.value || "ar";

    const headers = {
      "Accept-Language": lang,
      "Content-Type": "application/json",
      ...(options.headers || {}),
    };

    return this.processRequest<T>(path, {
      ...options,
      headers,
    });
  }

  /**
   * Makes a request to the API with FormData
   */
  protected async requestWithFormData<T>(
    path: string,
    options: RequestInit & { body: FormData },
  ): Promise<T> {
    const cookieStore = await cookies();
    const lang = cookieStore.get("NEXT_LOCALE")?.value || "ar";

    const headers = {
      "Accept-Language": lang,
      // Don't set Content-Type for FormData
      ...(options.headers || {}),
    };

    return this.processRequest<T>(path, {
      ...options,
      headers,
    });
  }

  /**
   * Processes the request and handles common response logic
   */
  private async processRequest<T>(
    path: string,
    options: RequestInit,
  ): Promise<T> {
    const requestId = Math.random().toString(36).substr(2, 9);

    const url = `${this.baseURL.replace(/\/$/, "")}/${path.replace(/^\//, "")}`;
    // Log the API request as a curl command
    const curlParts = [
      "curl",
      "-X",
      options.method || "GET",
      `'${this.baseURL}/${path}'`,
    ];

    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        curlParts.push("-H", `'${key}: ${value}'`);
      });
    }

    if (options.body) {
      if (typeof options.body === "string") {
        curlParts.push("--data", `'${options.body}'`);
      } else if (options.body instanceof FormData) {
        const entries = Array.from(options.body.entries());
        for (const [key, value] of entries) {
          curlParts.push("--form", `'${key}="${value}"'`);
        }
      }
    }

    console.debug(`🌐 [${requestId}] curl:`, curlParts.join(" "));

    try {
      const fetchOptions = {
        ...options,
        redirect: "follow" as RequestRedirect,
        mode: "cors" as RequestMode,
      };

      const res = await fetch(url, fetchOptions);

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));

        (errorData as any).status = res.status;
        (errorData as any).data = errorData;
        const error = new Error(
          errorData.error || errorData.errors?.[0]?.detail || res.statusText,
        ) as Error & { status?: number };
        error.status = res.status;
        console.log(error);
        throw error;
      }

      // Handle 204 No Content responses (e.g., DELETE operations)
      if (res.status === 204) {
        return undefined as T;
      }

      const data = await res.json();

      if (!data) {
        const error = new Error("No data returned from API");
        (error as any).status = res.status;
        throw error;
      }

      return data as T;
    } catch (error) {
      throw error;
    }
  }
}

export const getCoreSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();

  return cookieStore.get("core_session_token")?.value || null;
};

export const getMainToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("main_token")?.value || null;
};

export const getUserLanguage = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("NEXT_LOCALE")?.value || "ar";
};

export const getCurrSystem = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("currSystem")?.value || "core";
};
