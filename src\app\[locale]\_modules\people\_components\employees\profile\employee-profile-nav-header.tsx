"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "@/i18n/routing";
import { useTranslations } from "next-intl";
import {
  LeftCircleArrow,
  RightArrow,
} from "../../../../../../../../public/images/icons";
import { Skeleton } from "@/components/ui/skeleton";
import detectLanguageSimple from "@/utils/detect-text-langauge";

type EmployeeProfileHeaderProps = {
  employeeName: string;
  isLoading: boolean;
};

const EmployeeProfileNavHeader = ({
  employeeName,
  isLoading,
}: EmployeeProfileHeaderProps) => {
  const router = useRouter();
  const t = useTranslations();

  const handleBack = () => {
    router.push(`/people/employees`);
  };

  const textDir = detectLanguageSimple(employeeName);

  return (
    <div className="flex items-center gap-4 text-sm font-semibold leading-5 tracking-[0.5%]">
      <Button
        variant="ghost"
        title="go back to employees page"
        onClick={handleBack}
        className="w-5 h-5 p-0 outline-none"
      >
        <RightArrow className="!w-7 !h-6 ltr:rotate-180" />
      </Button>
      <LeftCircleArrow className="!w-7 !h-6 ltr:rotate-180" />
      <div className="flex items-center gap-2 text-secondary ms-2 lg:ms-6">
        <h1>{t("people.employees-page.table.title").split(" ")[1]}</h1>
        <span className="text-gray-300">/</span>
        <h2
          dir={textDir}
          className="text-gray-400 max-sm:truncate w-32 sm:w-full"
        >
          {isLoading ? (
            <Skeleton className="h-4 w-24 bg-gray-200 inline-block" />
          ) : (
            employeeName
          )}
        </h2>
      </div>
    </div>
  );
};

export default EmployeeProfileNavHeader;
