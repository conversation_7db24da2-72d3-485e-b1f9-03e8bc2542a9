import { SidebarData, TSystem, TSystems, TFunction } from "@/types";
import {
  HeartHealth,
  Calendar,
  Health,
  HealthHome,
  Report,
  SquaresFour,
  Appointment,
  Beneficiary,
  Video,
} from "../../public/images/health/icons";
import {
  <PERSON>,
  Profile,
  Profile2User,
  Element1,
  Box,
  DoubleNote,
  DollarSquare,
  CPUSettings,
  Settings,
  BriefCase,
} from "../../public/images/icons";
import { SYSTEM } from "./enum";
import { PermissionEnum } from "@/enums/Permission";

export const getSystemsArr = (t: TFunction): TSystem[] => {
  return [
    {
      href: `/${SYSTEM.PROCURE}`,
      name: `${SYSTEM.PROCURE}`,
      display_Name: t(`auth.selectSystem.procure.title`),
      title: "AtharProcure",
      description: t(`auth.selectSystem.procure.description`),
      img: "/images/systems-icons/AtharProcure.svg",
      alt: t(`auth.selectSystem.procure.alt`),
    },
    {
      href: `/${SYSTEM.PEOPLE}`,
      name: `${SYSTEM.PEOPLE}`,
      display_Name: t(`auth.selectSystem.people.title`),
      title: "AtharPeople",
      description: t(`auth.selectSystem.people.description`),
      img: "/images/systems-icons/AtharPeople.svg",
      alt: t(`auth.selectSystem.people.alt`),
    },
    {
      href: `/${SYSTEM.CM}`,
      name: `${SYSTEM.CM}`,
      display_Name: t(`auth.selectSystem.cm.title`),
      title: "AtharCM",
      description: t(`auth.selectSystem.cm.description`),
      img: "/images/systems-icons/AtharCM.svg",
      alt: t(`auth.selectSystem.cm.alt`),
    },
  ];
};

export const getSidebarData = (
  t: TFunction,
): Partial<Record<TSystems, SidebarData>> => {
  return {
    people: {
      head: "AtharHR",
      icon: "Profile2User",
      // links: ["الرئيسية", "الطلبات", "الرواتب"],
      items: [
        {
          title: t(`common.sidebar.links.people`),
          url: `/${SYSTEM.PEOPLE}`,
          icon: "SquaresFour",
          permission: [],
        },
        {
          title: t("common.sidebar.links.employees"),
          url: `/${SYSTEM.PEOPLE}/employees`,
          icon: "Profile2User",
          permission: [
            PermissionEnum.MANAGE_EMPLOYEE,
            PermissionEnum.READ_EMPLOYEE,
            PermissionEnum.CREATE_EMPLOYEE,
            PermissionEnum.UPDATE_EMPLOYEE,
          ],
        },
        {
          title: t("common.sidebar.links.requests"),
          url: `/${SYSTEM.PEOPLE}/requests`,
          icon: "DoubleNote",
          permission: [PermissionEnum.MANAGE_LEAVE, PermissionEnum.READ_LEAVE],
        },
        {
          title: t("common.sidebar.links.attendance"),
          url: `/${SYSTEM.PEOPLE}/attendance`,
          icon: "DoubleNote",
          permission: [],
        },
        {
          title: t("common.sidebar.links.salaries"),
          url: `/${SYSTEM.PEOPLE}/salaries`,
          icon: "DollarSquare",
          permission: [
            PermissionEnum.READ_SALARY_PACKAGE,
            PermissionEnum.READ_OWN_SALARY_PACKAGE,
            PermissionEnum.MANAGE_SALARY_PACKAGE,
            PermissionEnum.MANAGE_OTHERS_SALARY_PACKAGE,
            PermissionEnum.CREATE_SALARY_PACKAGE,
            PermissionEnum.CREATE_OTHERS_SALARY_PACKAGE,
            PermissionEnum.READ_SALARY_CALCULATION,
            PermissionEnum.READ_OWN_SALARY_CALCULATION,
          ],
        },
        {
          title: t("common.sidebar.links.devices"),
          url: `/${SYSTEM.PEOPLE}/devices`,
          icon: "CPUSettings",
          permission: [
            PermissionEnum.MANAGE_ATTENDANCE_DEVICE,
            PermissionEnum.READ_ATTENDANCE_DEVICE,
            PermissionEnum.CREATE_ATTENDANCE_DEVICE,
            PermissionEnum.UPDATE_ATTENDANCE_DEVICE,
            PermissionEnum.DESTROY_ATTENDANCE_DEVICE,
          ],
        },
        {
          title: t("common.sidebar.links.projects"),
          url: `/${SYSTEM.PEOPLE}/projects`,
          icon: "BriefCase",
          permission: [
            PermissionEnum.MANAGE_PROJECT,
            PermissionEnum.READ_PROJECT,
            PermissionEnum.CREATE_PROJECT,
            PermissionEnum.UPDATE_PROJECT,
          ],
        },
      ],
    },
    procure: {
      head: "AtherProcure",
      items: [
        {
          title: t(`common.sidebar.links.procure`),
          url: `/${SYSTEM.PROCURE}`,
          icon: "Element1",
          permission: [],
        },
        {
          title: t("common.sidebar.links.requests"),
          url: `/${SYSTEM.PROCURE}/requests`,
          icon: "Box",
          permission: [],
        },
        {
          title: t("common.sidebar.links.products"),
          url: `/${SYSTEM.PROCURE}/products`,
          icon: "Box",
          permission: [],
        },
      ],
    },
    cm: {
      head: "AtharCM",
      items: [
        {
          title: t(`common.sidebar.links.cm`),
          url: `/${SYSTEM.CM}`,
          icon: "SquaresFour",
          permission: [],
        },
        {
          title: t("common.sidebar.links.beneficiaries"),
          url: `/${SYSTEM.CM}/beneficiaries`,
          icon: "Calendar",
          permission: [],
        },
        {
          title: t("common.sidebar.links.appointments"),
          url: `/${SYSTEM.CM}/appointments`,
          icon: "Report",
          permission: [],
        },
      ],
    },
  };
};

export const DAYS = [
  { key: "monday", value: "monday" },
  { key: "tuesday", value: "tuesday" },
  { key: "wednesday", value: "wednesday" },
  { key: "thursday", value: "thursday" },
  { key: "friday", value: "friday" },
  { key: "saturday", value: "saturday" },
  { key: "sunday", value: "sunday" },
];

export const iconMap: Record<
  string,
  React.FC<React.SVGProps<SVGSVGElement>>
> = {
  HeartHealth,
  Calendar,
  Health,
  HealthHome,
  Report,
  SquaresFour,
  Bell,
  Profile,
  Appointment,
  Video,
  Beneficiary,
  Element1,
  Box,
  Profile2User,
  DoubleNote,
  DollarSquare,
  CPUSettings,
  Settings,
  BriefCase,
};
